<?php

namespace App\PostType\Tag\Model\Checker;

use App\Model\Orm\PriceLevel\PriceLevelRepository;
use App\Model\Orm\Product\Product;
use App\Model\Orm\Product\ProductRepository;
use App\Model\Orm\ProductLocalization\ProductLocalization;
use App\Model\Orm\ProductType\ProductType;
use App\Model\Orm\ProductVariant\ProductVariant;
use App\Model\Orm\ProductVariant\ProductVariantRepository;
use App\PostType\Tag\Model\Orm\Tag\TagRepository;
use App\PostType\Tag\Model\TagType;
use Closure;

class Checker
{

	public function __construct(
		private readonly ProductRepository $productRepository,
		private readonly ProductVariantRepository $productVariantRepository,
		private readonly PriceLevelRepository $priceLevelRepository,
		private readonly TagRepository $tagRepository,
	)
	{
	}

	/**
	 * @phpstan-return  Closure(ProductLocalization $productLocalization, ProductVariant $productVariant): bool
	 */
	public function getCheckerFunction(TagType $tagType): Closure
	{
		return match ($tagType) {
			TagType::new => fn (ProductLocalization $productLocalization, ProductVariant $productVariant): bool => $this->productRepository->isNewProductByDateCreated($productLocalization->product),
			TagType::transitFree => fn (ProductLocalization $productLocalization, ProductVariant $productVariant): bool => $this->hasTransitFree($productVariant),
			TagType::paidByLo => fn (ProductLocalization $productLocalization, ProductVariant $productVariant): bool => $this->hasPaidByLo($productLocalization, $productVariant),
			TagType::paidByLoFull => fn (ProductLocalization $productLocalization, ProductVariant $productVariant): bool => $this->hasPaidByLoFull($productLocalization, $productVariant),
			TagType::giftFree => fn (ProductLocalization $productLocalization, ProductVariant $productVariant): bool => $this->hasPresent($productLocalization),
			TagType::promoPrice => fn (ProductLocalization $productLocalization, ProductVariant $productVariant): bool => $this->hasPromoPrice($productLocalization),
			default => fn (ProductLocalization $productLocalization, ProductVariant $productVariant): bool => false,
		};
	}

	private function hasPromoPrice(ProductLocalization $productLocalization): bool
	{
		$mutation = $productLocalization->mutation;
		$priceLevelDefault = $this->priceLevelRepository->getDefault();
		$productLocalization->product->setMutation($mutation);

		return $productLocalization->product->hasPromoPrice($mutation, $priceLevelDefault, $mutation->states->toCollection()->fetch());
	}

	private function hasTransitFree(ProductVariant $productVariant): bool
	{
		$hasFreeTransport = $this->productVariantRepository->getProductVariantWithFreeTransport()->getById($productVariant->id) !== null;
		// Additional check: only for regular products (not services, etc.)
		$isRegularProduct = $productVariant->product->productTypeId === Product::TYPE_DEFAULT;
		return $hasFreeTransport && $isRegularProduct;
	}

	private function hasPresent(ProductLocalization $productLocalization): bool
	{
		return $this->tagRepository->findProductsWithPresent()->getById($productLocalization->product->id) !== null;
	}

	private function hasPaidByLo(ProductLocalization $productLocalization, ProductVariant $productVariant): bool
	{
		// TODO: Implement logic for checking if product is paid by LO
		// This method should check if the product variant has some specific payment method or condition
		return false;
	}

	private function hasPaidByLoFull(ProductLocalization $productLocalization, ProductVariant $productVariant): bool
	{
		// TODO: Implement logic for checking if product is fully paid by LO
		// This method should check if the product variant has full payment by LO
		return false;
	}
}
