<?php

namespace App\PostType\Tag\Model;

use App\Model\Orm\Traits\EnumToArray; //phpcs:ignore

enum TagType: string
{

	use EnumToArray; //phpcs:ignore

	//system tags
	case transitFree = 'transitFree';
	case new = 'new';
	case promoPrice = 'promoPrice';
	case giftFree = 'giftFree';

	case blackFriday = 'blackFiday';
	case drakFriday = 'drakFiday';
	case videoReview = 'videoReview';
	case lastMinute = 'lastMinute';

	case custom = 'custom'; //without some system logic


	//previous system tags - TODO: remove
	case paidByLo = 'paidByLo';
	case paidByLoFull = 'paidByLoFull';
	case top = 'top';
	case bestseller = 'bestseller';
	case present = 'present';
	case preorder = 'preorder';

	public function isSystemTag(): bool
	{
		return match ($this) {
			self::custom => false,
			default => true
		};
	}

	public function isMutationDependTag(): bool
	{
		return ! in_array($this->value, $this->getMutationIndependentTag());
	}


	private function getMutationIndependentTag(): array
	{
		return [];
	}

//	public function getProductProperty(): string|null
//	{
//		return match ($this) {
//			self::preorder => 'IsInPrepare',
//			self::transitFree => ['isFreeTransport', 'isFreeTransportForced'],
//			default => null,
//		};
//	}

}
