<?php declare(strict_types = 1);

namespace App\FrontModule\Components\OpenGraph\DTO;

use App\FrontModule\Presenters\BasePresenter;
use App\Model\Setup;
use App\Model\Orm\ImageEntity;
use App\Model\StaticPage\StaticPage;
use stdClass;
use App\Model\Image\ImageObject;
use Nette\Utils\Strings;

abstract class OpenGraphDto implements OpenGraphDtoInterface
{

	private mixed $data;

	private array $config;

	private BasePresenter $presenter;

	protected Setup $setup;

	protected ?stdClass $imageInfo = null;

	final public function setData(mixed $data): OpenGraphDto
	{
		$this->data = $data;
		return $this;
	}

	final public function setSetup(Setup $setup): OpenGraphDto
	{
		$this->setup = $setup;
		return $this;
	}

	final protected function getData(): mixed
	{
		return $this->data;
	}

	final public function setConfig(array $config): OpenGraphDto
	{
		$this->config = $config;
		return $this;
	}
	final protected function getConfig(): array
	{
		return $this->config;
	}

	final public function setPresenter(BasePresenter $presenter): OpenGraphDto
	{
		$this->presenter = $presenter;
		return $this;
	}

	final protected function getPresenter(): BasePresenter
	{
		return $this->presenter;
	}

	public function init(): OpenGraphDto
	{
		bd(static::class);
		$this->imageInfo = $this->getImageInfo($this->getImageEntity());
		return $this;
	}

	abstract public function getTitle(): string|null;

	public function getType(): string|null
	{
		return $this->config['type'] ?? null;
	}

	abstract public function getUrl(): string|null;

	abstract public function getDescription(): string|null;

	public function getSiteName(): string|null
	{
		return $this->config['siteName'] ?? null;
	}

	public function getImage(): string|null
	{
		return $this->getPresenter()->getMutation()->getBaseUrlWithPrefix() . $this->imageInfo?->image->src;
	}

	public function getImageWidth(): int|null
	{
		return $this?->imageInfo->width ?? $this->config['imageWidth'];
	}

	public function getImageHeight(): int|null
	{
		return $this?->imageInfo->height ?? $this->config['imageHeight'];
	}

	public function getImageAlt(): string|null
	{
		return $this->getPresenter()->getMutation()->getRealDomainWithoutWWW();
	}

	protected function getImageEntity(): null|ImageEntity
	{
		if ($this->getData() instanceof StaticPage) {
			return null;
		}

		$data = $this->getData();

		if (is_object($data) && method_exists($data, 'getFirstImage')) {
			return $data->getFirstImage();
		}

		return null;
	}

	public function getImageInfo(?ImageEntity $imageEntity): stdClass|null
	{
		$imgSize = $this->config['imageSize'] ?? 'lg';

		/** @var ImageObject $image */
		$image = $imageEntity?->getSize($imgSize); //@phpstan-ignore-line
		if ($image === null) {
			$image = $this->getPresenter()->getMutation()->getOgImage($imgSize) ?? null;
			if ($image === null) {
				return null;
			}
		}

		$data = new stdClass();
		$data->image = $image;
		$data->url = $image->src;
		$data->width = $image->width;
		$data->height = $image->height;
		return $data;
	}

	protected function truncate(string $text, int $length = 160): string
	{
		return Strings::truncate(strip_tags($text), $length);
	}

}
