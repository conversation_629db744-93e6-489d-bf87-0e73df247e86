<?php

declare(strict_types=1);

namespace App\Model\Orm\ProductVariantPrice;

use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\ProductVariant\ProductVariant;
use App\Model\Orm\Traits\HasCamelCase;
use App\Model\Time\CurrentDateTimeProvider;
use Brick\Money\Currency;
use Nextras\Dbal\Result\Result;
use Nextras\Dbal\Result\Row;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Mapper\Dbal\DbalMapper;
use Nextras\Orm\Mapper\Mapper;

/**
 * @extends DbalMapper<ProductVariantPrice>
 */
final class ProductVariantPriceMapper extends DbalMapper
{

	use HasCamelCase;

	/**
* @return literal-string
*/
public function getTableName(): string
{
	return 'product_variant_price';
}

	/**
	 * @return ICollection<ProductVariantPrice>
	 */
	public function findAllInCurrency(string $currency): ICollection
	{
		$builder = $this->builder()->select('pvp.*')->from('product_variant_price', 'pvp');
		$builder->andWhere('pvp.price_currency = %s AND pvp.price_amount > 0', $currency);

		return $this->toCollection($builder);
	}
	/**
	 * @return ICollection<ProductVariantPrice>
	 */
	public function findAllWithDiscountPriceLevel(?string $currency = null): ICollection
	{
		$builder = $this->builder()->select('pvp.*')->from('product_variant_price', 'pvp');
		$builder->joinLeft('[price_level] AS [pl]', '[pvp.priceLevelId] = [pl.id]');
		$builder->andWhere('pl.discountPriceId IS NOT NULL');

		if ($currency !== null) {
			$builder->andWhere('pvp.price_currency = %s', $currency);
		}

		return $this->toCollection($builder);
	}

	/**
	 * @return array<Row>
	 */
	public function getFutureActivePriceData(?ProductVariant $variant, Mutation $mutation, PriceLevel $priceLevel, DateTimeImmutable $now): array
	{
		$query = $this->builder()->select('pvp.*')->from('product_variant_price', 'pvp');
		$query->andWhere('pvp.priceLevelId = %i', $priceLevel->id);
		$query->andWhere('pvp.productVariantId = %i', $variant->id);
		$query->andWhere('pvp.mutationId = %i', $mutation->id);
		$query->andWhere('pvp.validFrom is null or pvp.validFrom <= %dt', $now);
		$query->andWhere('pvp.validTo is null or pvp.validTo >= %dt', $now);

		$query->andWhere('pvp.price_amount > 0');

		$query->orderBy('price_amount asc');

		return $this->connection->queryByQueryBuilder($query)->fetchAll();
	}
}
