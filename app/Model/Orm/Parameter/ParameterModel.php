<?php declare(strict_types = 1);

namespace App\Model\Orm\Parameter;

use App\AdminModule\Presenters\Parameter\Components\ShellForm\FormData\BaseFormData;
use App\Model\Messenger\Parameter\Sort\SortMessage;
use App\Model\Orm\Orm;
use Nette\Utils\Random;
use Nette\Utils\Strings;
use RuntimeException;
use Symfony\Component\Messenger\MessageBusInterface;

final class ParameterModel
{

	public function __construct(
		private readonly Orm $orm,
		//private readonly MessageBusInterface $messageBus,
	)
	{
	}


	public function createNew(BaseFormData $data): Parameter
	{
		$parameter = new Parameter();
		$this->orm->parameter->attach($parameter);

		$parameter->name = $data->name;
		$parameter->type = $data->parameterType;
		$this->orm->parameter->persistAndFlush($parameter);

		return $parameter;
	}


	public function getUid(Parameter $parameter, string $uid, int $k = 0): string
	{
		if ($uid === '') {
			// aut. gener. UID
			$str = Strings::toAscii($parameter->name);
			$uid = lcfirst(str_replace(' ', '', ucwords(strtr($str, '_-', ' '))));
		}

		if ($uid === '') {
			$uid = Random::generate(5);
		}

		if ($k) {
			$uidToTest = $uid . '-' . $k;
		} else {
			$uidToTest = $uid;
		}
		$entityWithUid = $this->orm->parameter->findBy(['uid' => $uidToTest, 'id!=' => $parameter->id]);

		if ($entityWithUid->countStored()) {
			return $this->getUid($parameter, $uid, $k + 1);
		} else {
			return $uidToTest;
		}
	}


	public function remove(Parameter $object): void
	{
		if ($object->isProtected) {
			throw new RuntimeException('Cannot delete a protected parameter.');
		}

		$this->orm->parameter->delete($object);
	}

	public function reindexSort(Parameter $parameter): void
	{
		$parameter->pendingSort = true;
		$this->orm->persistAndFlush($parameter);
		//$this->messageBus->dispatch(new SortMessage($parameter->id, $parameter->typeSort));
	}

}
