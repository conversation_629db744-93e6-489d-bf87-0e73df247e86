<?php declare(strict_types = 1);

namespace App\Model\BucketFilter;

use App\Model\Orm\Parameter\Parameter;
use App\Model\Orm\ParameterValue\ParameterValue;
use App\Model\Orm\ParameterValue\ParameterValueRepository;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\RoutableEntity;
use App\Model\Orm\State\State;
use App\PostType\Page\Model\Orm\CatalogTree;

final class SortCreator
{
	public function __construct(
		private readonly ParameterValueRepository $parameterValueRepository,
	)
	{
	}


	public const TOP = 'top';
	public const CHEAPEST = 'cheapest';
	public const EXPENSIVE = 'expensive';
	public const BESTSELLER = 'bestseller';
	public const FULLTEXT = 'fulltextScore';

	public function create(string $order, State $state, PriceLevel $priceLevel, ?RoutableEntity $routableEntity = null): Sort
	{
		$sort = new Sort($order);

		if ($routableEntity instanceof CatalogTree && $routableEntity->hasCourses) {
			if ($order === self::TOP) {
				$sort->setIsPrefixed(false);

				$onlineParameterValue = $this->parameterValueRepository->getBy([
					'parameter->uid' => Parameter::UID_COURSE_TYPE,
					'internalAlias' => ParameterValue::ALIAS_ONLINE,
				]);
				$sort->addByClassType($onlineParameterValue);
				$sort->addByClassUpcomingDate();
			} elseif ($order === self::CHEAPEST) {
				$sort->addByPrice($state, $priceLevel, 'asc');
			} elseif ($order === self::EXPENSIVE) {
				$sort->addByPrice($state, $priceLevel, 'desc');
			}
		} else {
			// products
			$sort->setIsPrefixed(false);
		}

		//$sort->addByStore();
//		if ($order === 'top') {
//			$sort->setIsPrefixed(false);
//			$sort->setIsIndexed();
//			$sort->addByHasPriceLevelDefault();
//
//			$sort->addByComputedScore();
//		//} elseif ($order === 'score') {
//        //    $sort->addByScore();
//		} elseif ($order === self::BESTSELLER) {
//			$sort->setIsPrefixed(false);
//
//			$sort->addByBestseller();
//			$sort->addByComputedScore();
//		} elseif ($order === 'cheapest') {
//			$sort->setIsIndexed();
//			$sort->addByHasPriceLevelDefault();
//			$sort->addByPrice($state, $priceLevel, 'asc');
//		} elseif ($order === 'expensive') {
//			$sort->setIsIndexed();
//			$sort->addByHasPriceLevelDefault();
//			$sort->addByPrice($state, $priceLevel, 'desc');
//		} elseif ($order === 'review') {
//			$sort->addByReview();
//		} elseif ($order === 'newest') {
//			$sort->addByHasPriceLevelDefault();
//			$sort->setIsIndexed();
//
//			$sort->addByNewest();
//		} elseif ($order === 'oldest') {
//			$sort->addByHasPriceLevelDefault();
//			$sort->addByOldest();
//		} elseif ($order === 'name') {
//			$sort->setIsPrefixed(false);
//			$sort->setIsSuffixed();
//			$sort->addByHasPriceLevelDefault();
//			$sort->addByName();
//		} elseif ($order === 'fulltextScore') {
//			$sort->addByFulltextScore();
//			$sort->addByStoreCascade();
//		} elseif ($order === 'discount') {
//			$sort->setIsIndexed(false);
//			$sort->setIsPrefixed(false);
//			$sort->setIsSuffixed();
//
//			$sort->addByHasPriceLevelDefault();
//			$sort->addByDiscount();
//			$sort->addByComputedScore();
//		}

		return $sort;
	}

}
