{var $props = [
	title: 'Ceny, dostupnost, varianty',
	id: 'variants',
	icon: $templates.'/part/icons/coins.svg',
	variant: 'main',
	classes: ['u-mb-xxs'],
	tags: [
		[
			text: 'Lokalizované'
		]
	]
]}

{embed $templates.'/part/box/toggle.latte', props=>$props, templates=>$templates}
	{block content}

		{var $items = []}

		{foreach $form['variants']->getComponents() as $variantKey=>$variantContainer}
			{continueIf $variantKey === 'newItemMarker'}

			{var $variant = $product->variants->toCollection()->getById($variantKey)}
			{var $activeLangCodes = []}
			{var $activeVariants = []}

			{foreach $variant->variantLocalizations as $variantLocalizationKey=>$variantLocalization}
				{if $variantLocalization->active}
					{php $activeMutation = $variantLocalization->mutation}
					{php $activeLangCodes[] = [ text: $activeMutation->langCode ]}
				{/if}

			{/foreach}
			{var $itemName = sprintf('ERP ID: %s | %s', $variantLocalization->variant->extId, $variantLocalization->name)}
			{var $item = [
				data: [
					removeitem-target: 'item',
					controller: 'ProductVariant RemoveItem',
					action: 'ProductVariantEdit:updateValues@window->ProductVariant#updateValues',
					ProductVariant-id-value: 'variant_' . $variantKey,
				],
				tags: $activeLangCodes,
				texts: [
					[
						text: '<span data-ProductVariant-target="name">'.$itemName.'</span><span data-ProductVariant-target="price">'.'</span>',
					]
				],
				btnsAfter: [
					[
						icon: $templates.'/part/icons/pencil-alt.svg',
						tooltip: 'Editovat',
						extend: true,
						data: [
							controller: 'Toggle',
							action: 'Toggle#changeClass ProductVariant#edit',
							toggle-target-value: '#overlay-variant_'.$variantKey,
							toggle-target-class-value: 'is-visible'
						]
					],
					[
						icon: $templates.'/part/icons/trash.svg',
						tooltip: 'Odstranit',
						variant: 'remove',
						data: [
							action: 'RemoveItem#remove ProductVariant#remove'
						]
					]
				]
			]}

			{php $items[] = $item}
		{/foreach}

		{include $templates.'/part/box/list.latte',
			props: [
				data: [
					controller: 'List',
					List-name-value: 'variant',
				],
				listData: [
					List-target: 'list',
				],
				addData: [
					action: 'List#add',
				],
				add: true,
				dragdrop: true,
				items: $items
			]
		}
	{/block}
{/embed}











